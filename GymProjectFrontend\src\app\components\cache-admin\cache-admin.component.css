/* Cache Admin Component Styles */

/* Container */
.cache-admin-container {
  padding: 1.5rem;
  min-height: 100vh;
  background: var(--bg-color);
}

/* Header Section */
.cache-header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.header-title h4 {
  color: var(--text-primary);
  font-weight: 600;
}

.header-title p {
  font-size: 0.9rem;
  margin-top: 0.25rem;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Auto Refresh Toggle */
.auto-refresh-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--border-color);
  transition: 0.3s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--primary-color);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* Health Status Cards */
.health-status-section {
  margin-bottom: 2rem;
}

.stats-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--primary-color);
  transition: all 0.3s ease;
}

.stats-card.stats-success::before {
  background: var(--success-color);
}

.stats-card.stats-danger::before {
  background: var(--danger-color);
}

.stats-card.stats-warning::before {
  background: var(--warning-color);
}

.stats-card.stats-info::before {
  background: var(--info-color);
}

.stats-card.stats-primary::before {
  background: var(--primary-color);
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  background: var(--primary-color);
}

.stats-success .stats-icon {
  background: var(--success-color);
}

.stats-danger .stats-icon {
  background: var(--danger-color);
}

.stats-warning .stats-icon {
  background: var(--warning-color);
}

.stats-info .stats-icon {
  background: var(--info-color);
}

.stats-content h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.stats-content p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* Tab Navigation */
.cache-tabs {
  margin-bottom: 2rem;
}

.tab-navigation {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tab-navigation::-webkit-scrollbar {
  display: none;
}

.tab-btn {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-weight: 500;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
  white-space: nowrap;
  cursor: pointer;
}

.tab-btn:hover {
  color: var(--primary-color);
  background: var(--hover-bg);
}

.tab-btn.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background: var(--active-bg);
}

.tab-btn i {
  font-size: 1.1rem;
}

/* Tab Content */
.tab-content-area {
  min-height: 400px;
}

.tab-content-panel {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

/* Entity Cards */
.entity-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  text-align: center;
  transition: all 0.3s ease;
}

.entity-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.entity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.5rem;
}

.entity-content h4 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.entity-content p {
  margin: 0;
  font-size: 0.8rem;
  color: var(--text-secondary);
  text-transform: capitalize;
}

/* Memory Usage */
.memory-usage-section {
  margin-bottom: 2rem;
}

.memory-progress {
  padding: 1rem 0;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.progress {
  height: 12px;
  background: var(--border-color);
  border-radius: 6px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  transition: width 0.6s ease;
  border-radius: 6px;
}

/* Search Section */
.search-section {
  margin-bottom: 2rem;
}

.search-input-group .input-group {
  box-shadow: var(--shadow-sm);
}

.search-input-group .input-group-text {
  background: var(--card-bg);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

/* Keys Results */
.keys-results-section {
  margin-bottom: 2rem;
}

.results-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pagination-controls {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 1rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.page-size-selector {
  min-width: 120px;
}

.page-navigation {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-info {
  padding: 0 1rem;
  font-weight: 500;
  color: var(--text-secondary);
}

/* Keys Table */
.keys-table-container {
  margin-top: 1rem;
}

.modern-table-responsive {
  overflow-x: auto;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.modern-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--card-bg);
}

.modern-table th {
  background: var(--header-bg);
  color: var(--text-primary);
  font-weight: 600;
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  white-space: nowrap;
}

.modern-table td {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
  vertical-align: middle;
}

.modern-table tbody tr:hover {
  background: var(--hover-bg);
}

.table-row-animated {
  animation: slideInUp 0.3s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.cache-key-code {
  background: var(--code-bg);
  color: var(--code-color);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  word-break: break-all;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
}

.empty-state-content i {
  opacity: 0.5;
}

/* Action Cards */
.action-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--primary-color);
}

.action-card.action-danger::before {
  background: var(--danger-color);
}

.action-card.action-warning::before {
  background: var(--warning-color);
}

.action-card.action-info::before {
  background: var(--info-color);
}

.action-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 1.5rem;
}

.action-danger .action-icon {
  background: var(--danger-color);
}

.action-warning .action-icon {
  background: var(--warning-color);
}

.action-info .action-icon {
  background: var(--info-color);
}

.action-content h6 {
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.action-content p {
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* Code Block */
.code-block {
  background: var(--code-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  margin-top: 1rem;
  overflow-x: auto;
}

.code-block pre {
  margin: 0;
  color: var(--code-color);
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  line-height: 1.4;
}

.code-block code {
  background: none;
  color: inherit;
  padding: 0;
}

/* Warmup Config */
.warmup-config-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  text-align: center;
  transition: all 0.3s ease;
}

.warmup-config-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.warmup-config-card h6 {
  margin-bottom: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.warmup-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-check {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--hover-bg);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.form-check:hover {
  background: var(--active-bg);
}

.form-check-input {
  width: 18px;
  height: 18px;
  margin: 0;
}

.form-check-label {
  margin: 0;
  font-weight: 500;
  color: var(--text-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
}

.warmup-actions {
  text-align: center;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

/* Monitoring */
.monitoring-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.auto-refresh-indicator {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: var(--success-color);
}

.blink {
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

.metric-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.metric-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.metric-content h4 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.metric-content p {
  margin: 0;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.metric-trend {
  margin-top: 0.25rem;
  font-size: 0.8rem;
}

/* Connection Status */
.connection-status {
  margin-top: 2rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.status-connected {
  background: rgba(var(--success-rgb), 0.1);
  border-color: var(--success-color);
}

.status-disconnected {
  background: rgba(var(--danger-rgb), 0.1);
  border-color: var(--danger-color);
}

.status-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.status-connected .status-icon {
  background: var(--success-color);
  color: white;
}

.status-disconnected .status-icon {
  background: var(--danger-color);
  color: white;
}

.status-content h6 {
  margin: 0;
  font-weight: 600;
}

.status-content p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* Tenant Details */
.tenant-info {
  padding: 1rem 0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
  border-bottom: none;
}

.info-item label {
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.info-item span,
.info-item code {
  color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .cache-admin-container {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .tab-navigation {
    flex-wrap: wrap;
  }

  .tab-btn {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }

  .pagination-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .page-navigation {
    justify-content: center;
  }

  .stats-card {
    flex-direction: column;
    text-align: center;
  }

  .metric-card {
    flex-direction: column;
    text-align: center;
  }

  .action-buttons {
    flex-direction: column;
  }

  .monitoring-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}

@media (max-width: 576px) {
  .cache-admin-container {
    padding: 0.5rem;
  }

  .stats-card,
  .action-card,
  .metric-card {
    padding: 1rem;
  }

  .modern-table th,
  .modern-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.9rem;
  }

  .cache-key-code {
    font-size: 0.75rem;
  }
}
