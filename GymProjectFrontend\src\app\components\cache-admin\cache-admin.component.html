<!-- <PERSON><PERSON> Admin Panel -->
<div class="cache-admin-container">
  <!-- Loading Overlay -->
  <app-loading-spinner
    *ngIf="isLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Cache verileri yükleniyor...">
  </app-loading-spinner>

  <!-- Header Section -->
  <div class="cache-header">
    <div class="modern-card">
      <div class="modern-card-header">
        <div class="header-content">
          <div class="header-title">
            <h4 class="mb-0">
              <i class="fas fa-database me-2"></i>
              Cache Yönetim Paneli
            </h4>
            <p class="text-muted mb-0">Redis cache sisteminin yönetimi ve izlenmesi</p>
          </div>
          <div class="header-actions">
            <div class="auto-refresh-toggle">
              <label class="switch">
                <input type="checkbox" [(ngModel)]="autoRefresh" (change)="toggleAutoRefresh()">
                <span class="slider"></span>
              </label>
              <span class="ms-2">Otomatik Yenileme</span>
            </div>
            <button class="modern-btn modern-btn-outline-primary modern-btn-sm" (click)="refreshData()">
              <i class="fas fa-sync-alt me-1"></i>
              Yenile
            </button>
            <button class="modern-btn modern-btn-outline-success modern-btn-sm" (click)="testCacheConnection()">
              <i class="fas fa-heartbeat me-1"></i>
              Test
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Health Status Cards -->
  <div class="health-status-section" *ngIf="healthInfo">
    <div class="row g-3">
      <div class="col-xl-3 col-lg-6 col-md-6">
        <div class="stats-card" [ngClass]="healthInfo.isConnected ? 'stats-success' : 'stats-danger'">
          <div class="stats-icon">
            <i class="fas" [ngClass]="healthInfo.isConnected ? 'fa-check-circle' : 'fa-times-circle'"></i>
          </div>
          <div class="stats-content">
            <h3>{{ healthInfo.isConnected ? 'Bağlı' : 'Bağlantısız' }}</h3>
            <p>Cache Durumu</p>
          </div>
        </div>
      </div>
      <div class="col-xl-3 col-lg-6 col-md-6">
        <div class="stats-card stats-info">
          <div class="stats-icon">
            <i class="fas fa-clock"></i>
          </div>
          <div class="stats-content">
            <h3>{{ healthInfo.pingTime | number:'1.2-2' }}ms</h3>
            <p>Ping Süresi</p>
          </div>
        </div>
      </div>
      <div class="col-xl-3 col-lg-6 col-md-6">
        <div class="stats-card stats-primary">
          <div class="stats-icon">
            <i class="fas fa-tachometer-alt"></i>
          </div>
          <div class="stats-content">
            <h3>{{ healthInfo.responseTime | number }}ms</h3>
            <p>Response Time</p>
          </div>
        </div>
      </div>
      <div class="col-xl-3 col-lg-6 col-md-6">
        <div class="stats-card stats-warning">
          <div class="stats-icon">
            <i class="fas fa-server"></i>
          </div>
          <div class="stats-content">
            <h3>{{ healthInfo.serverInfo?.version || 'N/A' }}</h3>
            <p>Redis Version</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Navigation Tabs -->
  <div class="cache-tabs">
    <div class="modern-card">
      <div class="modern-card-header p-0">
        <div class="tab-navigation">
          <button class="tab-btn" 
                  [class.active]="activeTab === 'dashboard'" 
                  (click)="setActiveTab('dashboard')">
            <i class="fas fa-chart-pie"></i>
            <span>Dashboard</span>
          </button>
          <button class="tab-btn" 
                  [class.active]="activeTab === 'keys'" 
                  (click)="setActiveTab('keys')">
            <i class="fas fa-key"></i>
            <span>Cache Keys</span>
          </button>
          <button class="tab-btn" 
                  [class.active]="activeTab === 'management'" 
                  (click)="setActiveTab('management')">
            <i class="fas fa-cogs"></i>
            <span>Yönetim</span>
          </button>
          <button class="tab-btn" 
                  [class.active]="activeTab === 'warmup'" 
                  (click)="setActiveTab('warmup')">
            <i class="fas fa-fire"></i>
            <span>Warmup</span>
          </button>
          <button class="tab-btn" 
                  [class.active]="activeTab === 'monitoring'" 
                  (click)="setActiveTab('monitoring')">
            <i class="fas fa-chart-line"></i>
            <span>Monitoring</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Tab Content -->
  <div class="tab-content-area">
    <!-- Dashboard Tab -->
    <div *ngIf="activeTab === 'dashboard'" class="tab-content-panel fade-in">
      <!-- Statistics Overview -->
      <div class="statistics-section" *ngIf="statistics">
        <div class="row g-3 mb-4">
          <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="stats-card stats-primary">
              <div class="stats-icon">
                <i class="fas fa-building"></i>
              </div>
              <div class="stats-content">
                <h3>{{ statistics.companyId }}</h3>
                <p>Company ID</p>
              </div>
            </div>
          </div>
          <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="stats-card stats-success">
              <div class="stats-icon">
                <i class="fas fa-key"></i>
              </div>
              <div class="stats-content">
                <h3>{{ statistics.totalKeys | number }}</h3>
                <p>Toplam Keys</p>
              </div>
            </div>
          </div>
          <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="stats-card stats-info">
              <div class="stats-icon">
                <i class="fas fa-memory"></i>
              </div>
              <div class="stats-content">
                <h3>{{ statistics.totalMemoryUsageMB | number:'1.1-1' }} MB</h3>
                <p>Bellek Kullanımı</p>
              </div>
            </div>
          </div>
          <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="stats-card stats-warning">
              <div class="stats-icon">
                <i class="fas fa-percentage"></i>
              </div>
              <div class="stats-content">
                <h3>{{ getCacheHitRatio() }}%</h3>
                <p>Hit Oranı</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Entity Distribution -->
        <div class="modern-card" *ngIf="statistics.keysByEntity">
          <div class="modern-card-header">
            <h5 class="mb-0">
              <i class="fas fa-chart-pie me-2"></i>
              Entity Dağılımı
            </h5>
          </div>
          <div class="modern-card-body">
            <div class="row g-3">
              <div *ngFor="let entity of statistics.keysByEntity | keyvalue; let i = index" 
                   class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                <div class="entity-card">
                  <div class="entity-icon">
                    <i class="fas fa-database"></i>
                  </div>
                  <div class="entity-content">
                    <h4>{{ entity.value | number }}</h4>
                    <p>{{ entity.key }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Memory Usage Chart -->
      <div class="memory-usage-section">
        <div class="modern-card">
          <div class="modern-card-header">
            <h5 class="mb-0">
              <i class="fas fa-chart-area me-2"></i>
              Bellek Kullanımı
            </h5>
          </div>
          <div class="modern-card-body">
            <div class="memory-progress">
              <div class="progress-info">
                <span>Kullanılan: {{ statistics?.totalMemoryUsageMB | number:'1.1-1' }} MB</span>
                <span>{{ getMemoryUsagePercentage() | number:'1.1-1' }}%</span>
              </div>
              <div class="progress">
                <div class="progress-bar" 
                     [style.width.%]="getMemoryUsagePercentage()"
                     [ngClass]="{
                       'bg-success': getMemoryUsagePercentage() < 60,
                       'bg-warning': getMemoryUsagePercentage() >= 60 && getMemoryUsagePercentage() < 80,
                       'bg-danger': getMemoryUsagePercentage() >= 80
                     }">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Cache Keys Tab -->
    <div *ngIf="activeTab === 'keys'" class="tab-content-panel fade-in">
      <!-- Search Section -->
      <div class="search-section">
        <div class="modern-card">
          <div class="modern-card-header">
            <h5 class="mb-0">
              <i class="fas fa-search me-2"></i>
              Cache Key Arama
            </h5>
          </div>
          <div class="modern-card-body">
            <div class="row g-3">
              <div class="col-lg-8">
                <div class="search-input-group">
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="fas fa-search"></i>
                    </span>
                    <input type="text"
                           class="modern-form-control"
                           [(ngModel)]="searchPattern"
                           placeholder="Pattern girin (örn: member:*, payment:123, *)"
                           (keyup.enter)="searchKeys()">
                    <button class="modern-btn modern-btn-primary" (click)="searchKeys()">
                      <i class="fas fa-search me-1"></i>
                      Ara
                    </button>
                  </div>
                </div>
              </div>
              <div class="col-lg-4">
                <select class="modern-form-control"
                        [(ngModel)]="selectedPattern"
                        (change)="applyPredefinedPattern(selectedPattern)">
                  <option value="">Hazır Pattern Seç</option>
                  <option *ngFor="let pattern of getPredefinedPatternKeys()"
                          [value]="pattern">{{ pattern }}</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Cache Keys Results -->
      <div class="keys-results-section" *ngIf="cacheKeys">
        <div class="modern-card">
          <div class="modern-card-header">
            <div class="d-flex justify-content-between align-items-center">
              <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                Cache Keys Listesi
              </h5>
              <div class="results-info">
                <span class="modern-badge modern-badge-info">
                  {{ cacheKeys.pagination.totalCount }} toplam
                </span>
                <button class="modern-btn modern-btn-outline-primary modern-btn-sm ms-2"
                        (click)="loadCacheKeys()">
                  <i class="fas fa-sync-alt me-1"></i>
                  Yenile
                </button>
              </div>
            </div>
          </div>
          <div class="modern-card-body">
            <!-- Pagination Controls -->
            <div class="pagination-controls">
              <div class="page-size-selector">
                <select class="modern-form-control modern-form-control-sm"
                        [(ngModel)]="pageSize"
                        (change)="changePageSize(pageSize)">
                  <option [value]="25">25 / sayfa</option>
                  <option [value]="50">50 / sayfa</option>
                  <option [value]="100">100 / sayfa</option>
                </select>
              </div>
              <div class="page-navigation">
                <button class="modern-btn modern-btn-outline-secondary modern-btn-sm"
                        [disabled]="currentPage <= 1"
                        (click)="changePage(currentPage - 1)">
                  <i class="fas fa-chevron-left"></i>
                </button>
                <span class="page-info">
                  {{ currentPage }} / {{ Math.ceil(cacheKeys.pagination.totalCount / pageSize) }}
                </span>
                <button class="modern-btn modern-btn-outline-secondary modern-btn-sm"
                        [disabled]="currentPage >= Math.ceil(cacheKeys.pagination.totalCount / pageSize)"
                        (click)="changePage(currentPage + 1)">
                  <i class="fas fa-chevron-right"></i>
                </button>
              </div>
            </div>

            <!-- Keys Table -->
            <div class="keys-table-container">
              <div class="modern-table-responsive">
                <table class="modern-table">
                  <thead>
                    <tr>
                      <th>Cache Key</th>
                      <th>Entity</th>
                      <th>Boyut</th>
                      <th>TTL</th>
                      <th>Oluşturulma</th>
                      <th>İşlemler</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let key of cacheKeys.keys; let i = index"
                        class="table-row-animated"
                        [style.animation-delay]="(i * 0.05) + 's'">
                      <td>
                        <code class="cache-key-code">{{ key.key }}</code>
                      </td>
                      <td>
                        <span class="modern-badge modern-badge-secondary">
                          {{ key.type || 'N/A' }}
                        </span>
                      </td>
                      <td>
                        <span class="modern-badge modern-badge-info">
                          {{ formatBytes(key.memoryUsage) }}
                        </span>
                      </td>
                      <td>
                        <span class="modern-badge"
                              [ngClass]="key.ttl && key.ttl > 0 ? 'modern-badge-warning' : 'modern-badge-success'">
                          {{ key.ttl && key.ttl > 0 ? (key.ttl + 's') : 'Permanent' }}
                        </span>
                      </td>
                      <td class="text-muted">
                        {{ formatDateTime(key.createdAt) }}
                      </td>
                      <td>
                        <div class="action-buttons">
                          <button class="modern-btn modern-btn-outline-info modern-btn-sm"
                                  (click)="getCacheKeyValue(key.key)"
                                  title="Değeri Görüntüle">
                            <i class="fas fa-eye"></i>
                          </button>
                          <button class="modern-btn modern-btn-outline-danger modern-btn-sm"
                                  (click)="deleteCacheKey(key.key)"
                                  title="Sil">
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- Empty State -->
              <div *ngIf="cacheKeys.keys.length === 0" class="empty-state">
                <div class="empty-state-content">
                  <i class="fas fa-search fa-3x text-muted mb-3"></i>
                  <h5 class="text-muted">Cache key bulunamadı</h5>
                  <p class="text-muted">Arama kriterlerinizi değiştirip tekrar deneyin.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Management Tab -->
    <div *ngIf="activeTab === 'management'" class="tab-content-panel fade-in">
      <!-- Quick Actions -->
      <div class="quick-actions-section">
        <div class="row g-3 mb-4">
          <div class="col-lg-4 col-md-6">
            <div class="action-card action-danger">
              <div class="action-icon">
                <i class="fas fa-trash-alt"></i>
              </div>
              <div class="action-content">
                <h6>Tüm Cache Temizle</h6>
                <p>Sistemdeki tüm cache verilerini temizler</p>
                <button class="modern-btn modern-btn-danger w-100" (click)="clearAllCache()">
                  <i class="fas fa-trash-alt me-1"></i>
                  Tümünü Temizle
                </button>
              </div>
            </div>
          </div>
          <div class="col-lg-4 col-md-6">
            <div class="action-card action-warning">
              <div class="action-icon">
                <i class="fas fa-broom"></i>
              </div>
              <div class="action-content">
                <h6>Şirket Cache Temizle</h6>
                <p>Mevcut şirketin cache'ini temizler</p>
                <button class="modern-btn modern-btn-warning w-100" (click)="clearCompanyCache()">
                  <i class="fas fa-broom me-1"></i>
                  Şirket Cache Temizle
                </button>
              </div>
            </div>
          </div>
          <div class="col-lg-4 col-md-6">
            <div class="action-card action-info">
              <div class="action-icon">
                <i class="fas fa-sync-alt"></i>
              </div>
              <div class="action-content">
                <h6>Cache Yenile</h6>
                <p>Cache verilerini yeniden yükler</p>
                <button class="modern-btn modern-btn-info w-100" (click)="refreshData()">
                  <i class="fas fa-sync-alt me-1"></i>
                  Yenile
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pattern-based Operations -->
      <div class="pattern-operations-section">
        <div class="modern-card">
          <div class="modern-card-header">
            <h5 class="mb-0">
              <i class="fas fa-filter me-2"></i>
              Pattern Bazlı İşlemler
            </h5>
          </div>
          <div class="modern-card-body">
            <div class="row g-3">
              <div class="col-lg-8">
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="fas fa-search"></i>
                  </span>
                  <input type="text"
                         class="modern-form-control"
                         [(ngModel)]="selectedPattern"
                         placeholder="Pattern girin (örn: member:*, payment:*)">
                  <button class="modern-btn modern-btn-danger"
                          (click)="clearCacheByPattern()"
                          [disabled]="!selectedPattern.trim()">
                    <i class="fas fa-trash me-1"></i>
                    Pattern Temizle
                  </button>
                </div>
              </div>
              <div class="col-lg-4">
                <select class="modern-form-control" [(ngModel)]="selectedPattern">
                  <option value="">Hazır Pattern Seç</option>
                  <option *ngFor="let pattern of getPredefinedPatternKeys()"
                          [value]="predefinedPatterns[pattern]">{{ pattern }}</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Debug Area -->
      <div class="debug-section">
        <div class="modern-card">
          <div class="modern-card-header">
            <h5 class="mb-0">
              <i class="fas fa-bug me-2"></i>
              Debug Alanı
            </h5>
          </div>
          <div class="modern-card-body">
            <div class="row g-3">
              <div class="col-lg-8">
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="fas fa-key"></i>
                  </span>
                  <input type="text"
                         class="modern-form-control"
                         [(ngModel)]="selectedKey"
                         placeholder="Cache key girin"
                         (keyup.enter)="getCacheKeyValue()">
                  <button class="modern-btn modern-btn-primary"
                          (click)="getCacheKeyValue()"
                          [disabled]="!selectedKey.trim()">
                    <i class="fas fa-eye me-1"></i>
                    Değeri Görüntüle
                  </button>
                </div>
              </div>
            </div>

            <!-- Key Value Display -->
            <div *ngIf="showKeyValue && keyValue" class="key-value-display mt-3">
              <h6>Cache Değeri:</h6>
              <div class="code-block">
                <pre><code>{{ formatJson(keyValue) }}</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Warmup Tab -->
    <div *ngIf="activeTab === 'warmup'" class="tab-content-panel fade-in">
      <!-- Quick Warmup Configs -->
      <div class="quick-warmup-section">
        <div class="modern-card">
          <div class="modern-card-header">
            <h5 class="mb-0">
              <i class="fas fa-rocket me-2"></i>
              Hızlı Warmup Konfigürasyonları
            </h5>
          </div>
          <div class="modern-card-body">
            <div class="row g-3">
              <div *ngFor="let config of getQuickWarmupConfigKeys()" class="col-lg-3 col-md-6">
                <div class="warmup-config-card">
                  <h6>{{ config }}</h6>
                  <button class="modern-btn modern-btn-outline-primary w-100"
                          (click)="applyQuickWarmupConfig(config)">
                    <i class="fas fa-download me-1"></i>
                    Uygula
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Warmup Configuration -->
      <div class="warmup-config-section">
        <div class="modern-card">
          <div class="modern-card-header">
            <h5 class="mb-0">
              <i class="fas fa-cogs me-2"></i>
              Warmup Konfigürasyonu
            </h5>
          </div>
          <div class="modern-card-body">
            <div class="row g-3">
              <div class="col-lg-6">
                <div class="warmup-options">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox"
                           [(ngModel)]="warmupConfig.warmupMembers"
                           id="warmupMembers">
                    <label class="form-check-label" for="warmupMembers">
                      <i class="fas fa-users me-2"></i>
                      Üye Verileri
                    </label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox"
                           [(ngModel)]="warmupConfig.warmupPayments"
                           id="warmupPayments">
                    <label class="form-check-label" for="warmupPayments">
                      <i class="fas fa-credit-card me-2"></i>
                      Ödeme Verileri
                    </label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox"
                           [(ngModel)]="warmupConfig.warmupMemberships"
                           id="warmupMemberships">
                    <label class="form-check-label" for="warmupMemberships">
                      <i class="fas fa-id-card me-2"></i>
                      Üyelik Verileri
                    </label>
                  </div>
                </div>
              </div>
              <div class="col-lg-6">
                <div class="warmup-options">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox"
                           [(ngModel)]="warmupConfig.warmupUsers"
                           id="warmupUsers">
                    <label class="form-check-label" for="warmupUsers">
                      <i class="fas fa-user me-2"></i>
                      Kullanıcı Verileri
                    </label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox"
                           [(ngModel)]="warmupConfig.warmupCompanySettings"
                           id="warmupCompanySettings">
                    <label class="form-check-label" for="warmupCompanySettings">
                      <i class="fas fa-building me-2"></i>
                      Şirket Ayarları
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <div class="warmup-actions mt-4">
              <button class="modern-btn modern-btn-success"
                      (click)="warmupCache()"
                      [disabled]="isWarmupRunning">
                <i class="fas" [ngClass]="isWarmupRunning ? 'fa-spinner fa-spin' : 'fa-fire'"></i>
                <span class="ms-1">{{ isWarmupRunning ? 'Warmup Çalışıyor...' : 'Warmup Başlat' }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Warmup Status -->
      <div class="warmup-status-section" *ngIf="warmupStatus">
        <div class="modern-card">
          <div class="modern-card-header">
            <h5 class="mb-0">
              <i class="fas fa-chart-bar me-2"></i>
              Son Warmup Sonuçları
            </h5>
          </div>
          <div class="modern-card-body">
            <div class="row g-3">
              <div class="col-lg-3 col-md-6">
                <div class="stats-card stats-success">
                  <div class="stats-icon">
                    <i class="fas fa-clock"></i>
                  </div>
                  <div class="stats-content">
                    <h3>{{ warmupStatus.totalDuration }}ms</h3>
                    <p>Toplam Süre</p>
                  </div>
                </div>
              </div>
              <div class="col-lg-3 col-md-6">
                <div class="stats-card stats-info">
                  <div class="stats-icon">
                    <i class="fas fa-check"></i>
                  </div>
                  <div class="stats-content">
                    <h3>{{ warmupStatus.successCount }}</h3>
                    <p>Başarılı</p>
                  </div>
                </div>
              </div>
              <div class="col-lg-3 col-md-6">
                <div class="stats-card stats-warning">
                  <div class="stats-icon">
                    <i class="fas fa-times"></i>
                  </div>
                  <div class="stats-content">
                    <h3>{{ warmupStatus.errorCount }}</h3>
                    <p>Hatalı</p>
                  </div>
                </div>
              </div>
              <div class="col-lg-3 col-md-6">
                <div class="stats-card stats-primary">
                  <div class="stats-icon">
                    <i class="fas fa-database"></i>
                  </div>
                  <div class="stats-content">
                    <h3>{{ warmupStatus.totalRecords }}</h3>
                    <p>Toplam Kayıt</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Monitoring Tab -->
    <div *ngIf="activeTab === 'monitoring'" class="tab-content-panel fade-in">
      <!-- Real-time Stats -->
      <div class="realtime-stats-section">
        <div class="modern-card">
          <div class="modern-card-header">
            <div class="d-flex justify-content-between align-items-center">
              <h5 class="mb-0">
                <i class="fas fa-chart-line me-2"></i>
                Gerçek Zamanlı İzleme
              </h5>
              <div class="monitoring-controls">
                <div class="auto-refresh-indicator" *ngIf="autoRefresh">
                  <i class="fas fa-circle text-success blink"></i>
                  <span class="ms-1">Canlı</span>
                </div>
                <select class="modern-form-control modern-form-control-sm ms-2"
                        [(ngModel)]="refreshInterval"
                        (change)="autoRefresh && setupAutoRefresh()">
                  <option [value]="10">10 saniye</option>
                  <option [value]="30">30 saniye</option>
                  <option [value]="60">1 dakika</option>
                  <option [value]="300">5 dakika</option>
                </select>
              </div>
            </div>
          </div>
          <div class="modern-card-body">
            <!-- Performance Metrics -->
            <div class="performance-metrics" *ngIf="healthInfo && statistics">
              <div class="row g-3">
                <div class="col-lg-3 col-md-6">
                  <div class="metric-card">
                    <div class="metric-icon">
                      <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <div class="metric-content">
                      <h4>{{ healthInfo.responseTime }}ms</h4>
                      <p>Response Time</p>
                      <div class="metric-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="text-success">+2.3%</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-lg-3 col-md-6">
                  <div class="metric-card">
                    <div class="metric-icon">
                      <i class="fas fa-memory"></i>
                    </div>
                    <div class="metric-content">
                      <h4>{{ statistics.totalMemoryUsageMB | number:'1.1-1' }}MB</h4>
                      <p>Memory Usage</p>
                      <div class="metric-trend">
                        <i class="fas fa-arrow-down text-danger"></i>
                        <span class="text-danger">-1.2%</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-lg-3 col-md-6">
                  <div class="metric-card">
                    <div class="metric-icon">
                      <i class="fas fa-key"></i>
                    </div>
                    <div class="metric-content">
                      <h4>{{ statistics.totalKeys | number }}</h4>
                      <p>Total Keys</p>
                      <div class="metric-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="text-success">+5.7%</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-lg-3 col-md-6">
                  <div class="metric-card">
                    <div class="metric-icon">
                      <i class="fas fa-percentage"></i>
                    </div>
                    <div class="metric-content">
                      <h4>{{ getCacheHitRatio() }}%</h4>
                      <p>Hit Ratio</p>
                      <div class="metric-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                        <span class="text-success">+0.8%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Connection Status -->
            <div class="connection-status mt-4">
              <div class="status-indicator" [ngClass]="healthInfo?.isConnected ? 'status-connected' : 'status-disconnected'">
                <div class="status-icon">
                  <i class="fas" [ngClass]="healthInfo?.isConnected ? 'fa-check-circle' : 'fa-times-circle'"></i>
                </div>
                <div class="status-content">
                  <h6>{{ healthInfo?.isConnected ? 'Bağlantı Aktif' : 'Bağlantı Kesildi' }}</h6>
                  <p>Son kontrol: {{ formatDateTime(healthInfo?.lastChecked) }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Tenant Details -->
      <div class="tenant-details-section" *ngIf="tenantDetails">
        <div class="modern-card">
          <div class="modern-card-header">
            <h5 class="mb-0">
              <i class="fas fa-building me-2"></i>
              Tenant Cache Detayları
            </h5>
          </div>
          <div class="modern-card-body">
            <div class="tenant-info">
              <div class="row g-3">
                <div class="col-lg-6">
                  <div class="info-item">
                    <label>Company ID:</label>
                    <span class="modern-badge modern-badge-primary">{{ tenantDetails.companyId }}</span>
                  </div>
                  <div class="info-item">
                    <label>Toplam Keys:</label>
                    <span>{{ tenantDetails.totalKeys | number }}</span>
                  </div>
                  <div class="info-item">
                    <label>Bellek Kullanımı:</label>
                    <span>{{ tenantDetails.memoryUsageMB | number:'1.1-1' }} MB</span>
                  </div>
                </div>
                <div class="col-lg-6">
                  <div class="info-item">
                    <label>Son Güncelleme:</label>
                    <span>{{ formatDateTime(tenantDetails.lastUpdated) }}</span>
                  </div>
                  <div class="info-item">
                    <label>Cache Pattern:</label>
                    <code>gym:{{ tenantDetails.companyId }}:*</code>
                  </div>
                  <div class="info-item">
                    <label>Aktif Entities:</label>
                    <span>{{ tenantDetails.activeEntities?.length || 0 }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
