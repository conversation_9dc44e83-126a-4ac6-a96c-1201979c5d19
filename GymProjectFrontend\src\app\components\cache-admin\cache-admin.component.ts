import { Component, OnInit, OnDestroy } from '@angular/core';
import {
  CacheAdminService,
  CacheStatistics,
  CacheHealth,
  CacheKeysResponse,
  CacheKeyDetail,
  TenantCacheDetails,
  CacheWarmupRequest,
  CacheWarmupResult,
  CacheClearResult
} from '../../services/cache-admin.service';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from '../../services/auth.service';
import { Router } from '@angular/router';
import { interval, Subscription } from 'rxjs';

@Component({
  selector: 'app-cache-admin',
  templateUrl: './cache-admin.component.html',
  styleUrls: ['./cache-admin.component.css'],
  standalone: false
})
export class CacheAdminComponent implements OnInit, OnDestroy {
  // Ana veri modelleri
  statistics: CacheStatistics | null = null;
  healthInfo: CacheHealth | null = null;
  cacheKeys: CacheKeysResponse | null = null;
  tenantDetails: TenantCacheDetails | null = null;

  // UI durumu
  isLoading = false;
  activeTab = 'dashboard'; // dashboard, keys, management, warmup, monitoring
  autoRefresh = false;
  refreshInterval = 30; // saniye

  // Arama ve filtreleme
  searchPattern = '';
  selectedPattern = '';
  currentPage = 1;
  pageSize = 50;
  sortBy = 'key'; // key, size, ttl, type
  sortDirection = 'asc';

  // Cache yönetimi
  warmupConfig: CacheWarmupRequest = {
    warmupMembers: true,
    warmupPayments: true,
    warmupMemberships: true,
    warmupUsers: false,
    warmupCompanySettings: true
  };
  warmupStatus: any = null;
  isWarmupRunning = false;

  // Debug ve monitoring
  selectedKey = '';
  keyValue: any = null;
  showKeyValue = false;
  
  // Subscriptions
  private refreshSubscription?: Subscription;
  private autoRefreshSubscription?: Subscription;

  // Predefined patterns
  predefinedPatterns: { [key: string]: string } = {
    'Tüm Veriler': '*',
    'Üye Verileri': 'member:*',
    'Ödeme Verileri': 'payment:*',
    'Üyelik Verileri': 'membership:*',
    'Kullanıcı Verileri': 'user:*',
    'Şirket Ayarları': 'company:*',
    'Giriş Kayıtları': 'entry:*',
    'Antrenör Verileri': 'trainer:*',
    'Paket Verileri': 'package:*'
  };

  // Quick warmup configs
  quickWarmupConfigs: { [key: string]: CacheWarmupRequest } = {
    'Hızlı Başlangıç': {
      warmupMembers: true,
      warmupPayments: false,
      warmupMemberships: true,
      warmupUsers: false,
      warmupCompanySettings: true
    },
    'Tam Warmup': {
      warmupMembers: true,
      warmupPayments: true,
      warmupMemberships: true,
      warmupUsers: true,
      warmupCompanySettings: true
    },
    'Sadece Üyeler': {
      warmupMembers: true,
      warmupPayments: false,
      warmupMemberships: false,
      warmupUsers: false,
      warmupCompanySettings: false
    },
    'Finansal Veriler': {
      warmupMembers: false,
      warmupPayments: true,
      warmupMemberships: true,
      warmupUsers: false,
      warmupCompanySettings: false
    }
  };

  // Helper properties
  Math = Math;

  constructor(
    private cacheAdminService: CacheAdminService,
    private toastrService: ToastrService,
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Yetki kontrolü
    if (!this.authService.hasRole('owner')) {
      this.toastrService.error('Bu sayfaya erişim yetkiniz bulunmamaktadır.');
      this.router.navigate(['/unauthorized']);
      return;
    }

    this.loadInitialData();
    this.setupAutoRefresh();
  }

  ngOnDestroy(): void {
    this.stopAutoRefresh();
  }

  /**
   * İlk veri yükleme
   */
  async loadInitialData(): Promise<void> {
    this.isLoading = true;
    
    try {
      await Promise.all([
        this.loadStatistics(),
        this.loadHealthInfo(),
        this.loadTenantDetails()
      ]);
      
      // Keys sadece keys tab'ında yüklenecek
      if (this.activeTab === 'keys') {
        await this.loadCacheKeys();
      }
    } catch (error) {
      this.toastrService.error('Veriler yüklenirken hata oluştu');
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Cache istatistiklerini yükle
   */
  private loadStatistics(): Promise<void> {
    return new Promise((resolve) => {
      this.cacheAdminService.getStatistics().subscribe({
        next: (response) => {
          if (response.success) {
            this.statistics = response.data;
          } else {
            this.toastrService.error(response.message);
          }
          resolve();
        },
        error: (error) => {
          this.toastrService.error('Cache istatistikleri yüklenirken hata oluştu');
          resolve();
        }
      });
    });
  }

  /**
   * Cache sağlık durumunu yükle
   */
  private loadHealthInfo(): Promise<void> {
    return new Promise((resolve) => {
      this.cacheAdminService.getHealthInfo().subscribe({
        next: (response) => {
          if (response.success) {
            this.healthInfo = response.data;
          } else {
            this.toastrService.error(response.message);
          }
          resolve();
        },
        error: (error) => {
          this.toastrService.error('Cache sağlık bilgileri yüklenirken hata oluştu');
          resolve();
        }
      });
    });
  }

  /**
   * Tenant cache detaylarını yükle
   */
  private loadTenantDetails(): Promise<void> {
    return new Promise((resolve) => {
      this.cacheAdminService.getTenantCacheDetails().subscribe({
        next: (response) => {
          if (response.success) {
            this.tenantDetails = response.data;
          } else {
            this.toastrService.error(response.message);
          }
          resolve();
        },
        error: (error) => {
          this.toastrService.error('Tenant cache detayları yüklenirken hata oluştu');
          resolve();
        }
      });
    });
  }

  /**
   * Cache key'lerini yükle
   */
  loadCacheKeys(): Promise<void> {
    return new Promise((resolve) => {
      this.cacheAdminService.getCompanyCacheKeys(this.currentPage, this.pageSize).subscribe({
        next: (response) => {
          if (response.success) {
            this.cacheKeys = response.data;
          } else {
            this.toastrService.error(response.message);
          }
          resolve();
        },
        error: (error) => {
          this.toastrService.error('Cache key\'leri yüklenirken hata oluştu');
          resolve();
        }
      });
    });
  }

  /**
   * Auto refresh ayarları
   */
  setupAutoRefresh(): void {
    if (this.autoRefresh) {
      this.autoRefreshSubscription = interval(this.refreshInterval * 1000).subscribe(() => {
        this.refreshData();
      });
    }
  }

  toggleAutoRefresh(): void {
    this.autoRefresh = !this.autoRefresh;
    if (this.autoRefresh) {
      this.setupAutoRefresh();
      this.toastrService.info(`Otomatik yenileme ${this.refreshInterval} saniyede bir aktif`);
    } else {
      this.stopAutoRefresh();
      this.toastrService.info('Otomatik yenileme durduruldu');
    }
  }

  stopAutoRefresh(): void {
    if (this.autoRefreshSubscription) {
      this.autoRefreshSubscription.unsubscribe();
      this.autoRefreshSubscription = undefined;
    }
  }

  /**
   * Tab değiştirme
   */
  setActiveTab(tab: string): void {
    this.activeTab = tab;
    
    // Tab'a özel veri yükleme
    if (tab === 'keys' && !this.cacheKeys) {
      this.loadCacheKeys();
    }
  }

  /**
   * Verileri yenile
   */
  refreshData(): void {
    this.loadInitialData();
  }

  /**
   * Pattern ile arama
   */
  searchKeys(): void {
    if (!this.searchPattern.trim()) {
      this.loadCacheKeys();
      return;
    }

    this.isLoading = true;
    this.cacheAdminService.getKeysByPattern(this.searchPattern, this.currentPage, this.pageSize).subscribe({
      next: (response) => {
        if (response.success) {
          this.cacheKeys = response.data;
          this.toastrService.success(`${response.data.pagination.totalCount} adet key bulundu`);
        } else {
          this.toastrService.error(response.message);
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.toastrService.error('Arama yapılırken hata oluştu');
        this.isLoading = false;
      }
    });
  }

  /**
   * Predefined pattern uygula
   */
  applyPredefinedPattern(patternName: string): void {
    if (this.predefinedPatterns[patternName]) {
      this.searchPattern = this.predefinedPatterns[patternName];
      this.toastrService.info(`${patternName} pattern'i uygulandı`);
    }
  }

  /**
   * Cache temizleme işlemleri
   */
  clearAllCache(): void {
    if (!confirm('Tüm cache verilerini silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.')) {
      return;
    }

    this.isLoading = true;
    this.cacheAdminService.clearAllCache().subscribe({
      next: (response) => {
        if (response.success) {
          this.toastrService.success(`${response.data.removedCount} adet cache key silindi`);
          this.refreshData();
        } else {
          this.toastrService.error(response.message);
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.toastrService.error('Cache temizleme işlemi başarısız');
        this.isLoading = false;
      }
    });
  }

  clearCompanyCache(): void {
    if (!confirm('Şirket cache verilerini silmek istediğinizden emin misiniz?')) {
      return;
    }

    this.isLoading = true;
    this.cacheAdminService.clearTenantCache().subscribe({
      next: (response) => {
        if (response.success) {
          this.toastrService.success(`${response.data.removedCount} adet cache key silindi`);
          this.refreshData();
        } else {
          this.toastrService.error(response.message);
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.toastrService.error('Şirket cache temizleme işlemi başarısız');
        this.isLoading = false;
      }
    });
  }

  clearCacheByPattern(): void {
    if (!this.selectedPattern.trim()) {
      this.toastrService.warning('Lütfen bir pattern girin');
      return;
    }

    if (!confirm(`"${this.selectedPattern}" pattern'ine uygun cache verilerini silmek istediğinizden emin misiniz?`)) {
      return;
    }

    this.isLoading = true;
    this.cacheAdminService.clearCacheByPattern(this.selectedPattern).subscribe({
      next: (response) => {
        if (response.success) {
          this.toastrService.success(`${response.data.removedCount} adet cache key silindi`);
          this.refreshData();
        } else {
          this.toastrService.error(response.message);
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.toastrService.error('Pattern cache temizleme işlemi başarısız');
        this.isLoading = false;
      }
    });
  }

  /**
   * Cache warmup işlemleri
   */
  warmupCache(): void {
    if (!confirm('Cache warmup işlemini başlatmak istediğinizden emin misiniz?')) {
      return;
    }

    this.isWarmupRunning = true;
    this.isLoading = true;

    this.cacheAdminService.warmupCache(this.warmupConfig).subscribe({
      next: (response) => {
        if (response.success) {
          this.warmupStatus = response.data;
          this.toastrService.success(`Cache warmup tamamlandı. Süre: ${response.data.totalDuration}ms`);
          this.refreshData();
        } else {
          this.toastrService.error(response.message);
        }
        this.isWarmupRunning = false;
        this.isLoading = false;
      },
      error: (error) => {
        this.toastrService.error('Cache warmup işlemi başarısız');
        this.isWarmupRunning = false;
        this.isLoading = false;
      }
    });
  }

  applyQuickWarmupConfig(configName: string): void {
    if (this.quickWarmupConfigs[configName]) {
      this.warmupConfig = { ...this.quickWarmupConfigs[configName] };
      this.toastrService.info(`${configName} konfigürasyonu uygulandı`);
    }
  }

  /**
   * Cache key işlemleri
   */
  deleteCacheKey(key: string): void {
    if (!confirm(`"${key}" cache key'ini silmek istediğinizden emin misiniz?`)) {
      return;
    }

    this.cacheAdminService.deleteCacheKey(key).subscribe({
      next: (response) => {
        if (response.success) {
          this.toastrService.success(response.data.removed ? 'Cache key silindi' : 'Cache key bulunamadı');
          this.loadCacheKeys();
        } else {
          this.toastrService.error(response.message);
        }
      },
      error: (error) => {
        this.toastrService.error('Cache key silinirken hata oluştu');
      }
    });
  }

  getCacheKeyValue(key?: string): void {
    const keyToGet = key || this.selectedKey;
    if (!keyToGet.trim()) {
      this.toastrService.warning('Lütfen bir cache key girin');
      return;
    }

    this.cacheAdminService.getCacheKeyValue(keyToGet).subscribe({
      next: (response) => {
        if (response.success) {
          this.keyValue = response.data;
          this.showKeyValue = true;
          this.toastrService.success('Cache key değeri getirildi');
        } else {
          this.toastrService.error(response.message);
        }
      },
      error: (error) => {
        this.toastrService.error('Cache key değeri alınırken hata oluştu');
      }
    });
  }

  /**
   * Pagination işlemleri
   */
  changePage(page: number): void {
    this.currentPage = page;
    if (this.searchPattern.trim()) {
      this.searchKeys();
    } else {
      this.loadCacheKeys();
    }
  }

  changePageSize(size: number): void {
    this.pageSize = size;
    this.currentPage = 1;
    if (this.searchPattern.trim()) {
      this.searchKeys();
    } else {
      this.loadCacheKeys();
    }
  }

  /**
   * Utility metodlar
   */
  formatBytes(bytes: number): string {
    return this.cacheAdminService.formatMemoryUsage(bytes);
  }

  formatTTL(seconds?: number): string {
    return this.cacheAdminService.formatTTL(seconds);
  }

  formatDateTime(dateString?: string): string {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('tr-TR');
  }

  formatJson(obj: any): string {
    return JSON.stringify(obj, null, 2);
  }

  getHealthStatusColor(): string {
    if (!this.healthInfo) return 'text-muted';
    return this.healthInfo.isConnected ? 'text-success' : 'text-danger';
  }

  getMemoryUsagePercentage(): number {
    if (!this.statistics) return 0;
    // Bu değer backend'den gelecek, şimdilik mock
    return Math.min((this.statistics.totalMemoryUsageMB / 1024) * 100, 100);
  }

  getCacheHitRatio(): number {
    // Bu değer backend'den gelecek, şimdilik mock
    return 85;
  }

  getPredefinedPatternKeys(): string[] {
    return Object.keys(this.predefinedPatterns);
  }

  getQuickWarmupConfigKeys(): string[] {
    return Object.keys(this.quickWarmupConfigs);
  }

  testCacheConnection(): void {
    this.loadHealthInfo().then(() => {
      if (this.healthInfo?.isConnected) {
        this.toastrService.success('Cache bağlantısı başarılı!');
      } else {
        this.toastrService.error('Cache bağlantısında sorun var!');
      }
    });
  }
}
